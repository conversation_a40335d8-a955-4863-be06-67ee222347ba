"use client";

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
// NOTE: we load the heavy AOS bundle lazily so it doesn't block the main thread.
import 'aos/dist/aos.css';

export function AOSInit() {
  const pathname = usePathname();

  useEffect(() => {
    let aosInitialized = false;

    const ensureVisibility = () => {
      // Fallback: ensure all elements are visible if AOS fails to initialize
      if (!aosInitialized && typeof window !== 'undefined') {
        // Use class-based approach to avoid hydration issues
        document.body.classList.add('aos-fallback-visible');
      }
    };

    const initAOS = async () => {
      try {
        // Dynamically import AOS only when needed
        const AOS = (await import('aos')).default;

        // Initialize AOS with more reliable settings
        AOS.init({
          duration: 600,
          once: true, // Changed to true to prevent re-triggering issues
          offset: 50,
          easing: 'ease-out-cubic',
          delay: 0,
          // Remove mobile disable to ensure sections show on all devices
          disable: false, // Changed from window.innerWidth < 768
          disableMutationObserver: false,
          debounceDelay: 50,
          throttleDelay: 100,
          startEvent: 'DOMContentLoaded',
        });

        aosInitialized = true;
        // Add class to body to indicate AOS is initialized and remove fallback
        if (typeof window !== 'undefined') {
          document.body.classList.add('aos-init');
          document.body.classList.remove('aos-fallback-visible');
        }

        // Force refresh AOS after a short delay to ensure all elements are detected
        setTimeout(() => {
          AOS.refresh();
        }, 100);

        // Additional refresh after page is fully loaded
        setTimeout(() => {
          AOS.refresh();
        }, 500);
      } catch (error) {
        console.error('Failed to initialize AOS:', error);
        ensureVisibility();
      }
    };

    // Initialize immediately instead of using requestIdleCallback for more reliable loading
    // Only run on client side to avoid hydration issues
    if (typeof window !== 'undefined') {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAOS);
      } else {
        // DOM is already loaded
        initAOS();
      }

      // Fallback timeout to ensure visibility
      const fallbackTimeout = setTimeout(ensureVisibility, 2000);

      // Cleanup
      return () => {
        document.removeEventListener('DOMContentLoaded', initAOS);
        clearTimeout(fallbackTimeout);
      };
    }

  }, [pathname]); // Re-run when pathname changes

  return null; // This component doesn't render anything
}